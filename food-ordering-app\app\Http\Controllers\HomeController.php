<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Cuisine;
use App\Models\FoodItem;
use App\Models\Package;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Get featured items and packages
        $featuredFoodItems = FoodItem::featured()
            ->available()
            ->with(['category', 'cuisine'])
            ->limit(8)
            ->get();

        $featuredPackages = Package::featured()
            ->available()
            ->with(['category', 'cuisine'])
            ->limit(6)
            ->get();

        // Get popular items
        $popularFoodItems = FoodItem::popular()
            ->available()
            ->with(['category', 'cuisine'])
            ->limit(12)
            ->get();

        // Get active categories and cuisines for navigation
        $categories = Category::active()
            ->ordered()
            ->get();

        $cuisines = Cuisine::active()
            ->ordered()
            ->get();

        return view('home', compact(
            'featuredFoodItems',
            'featuredPackages',
            'popularFoodItems',
            'categories',
            'cuisines'
        ));
    }

    /**
     * Search for food items and packages.
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        $categoryId = $request->get('category');
        $cuisineId = $request->get('cuisine');
        $isVegetarian = $request->get('vegetarian');
        $isPopular = $request->get('popular');

        // Search food items
        $foodItemsQuery = FoodItem::available()->with(['category', 'cuisine']);
        
        if ($query) {
            $foodItemsQuery->search($query);
        }
        
        if ($categoryId) {
            $foodItemsQuery->byCategory($categoryId);
        }
        
        if ($cuisineId) {
            $foodItemsQuery->byCuisine($cuisineId);
        }
        
        if ($isVegetarian) {
            $foodItemsQuery->vegetarian();
        }
        
        if ($isPopular) {
            $foodItemsQuery->popular();
        }

        $foodItems = $foodItemsQuery->paginate(12, ['*'], 'food_page');

        // Search packages
        $packagesQuery = Package::available()->with(['category', 'cuisine']);
        
        if ($query) {
            $packagesQuery->search($query);
        }
        
        if ($categoryId) {
            $packagesQuery->byCategory($categoryId);
        }
        
        if ($cuisineId) {
            $packagesQuery->byCuisine($cuisineId);
        }
        
        if ($isVegetarian) {
            $packagesQuery->vegetarian();
        }
        
        if ($isPopular) {
            $packagesQuery->popular();
        }

        $packages = $packagesQuery->paginate(8, ['*'], 'package_page');

        // Get filter options
        $categories = Category::active()->ordered()->get();
        $cuisines = Cuisine::active()->ordered()->get();

        return view('search', compact(
            'foodItems',
            'packages',
            'categories',
            'cuisines',
            'query',
            'categoryId',
            'cuisineId',
            'isVegetarian',
            'isPopular'
        ));
    }
}
