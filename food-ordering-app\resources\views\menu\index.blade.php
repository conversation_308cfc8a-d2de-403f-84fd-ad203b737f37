@extends('layouts.app')

@section('title', 'Menu - Food Ordering App')

@section('content')
<!-- Quick Filters Slider (below nav bar) -->
<div class="bg-white border-b border-gray-200 sticky top-16 z-40">
    <div class="container-mobile">
        <div class="py-3">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <a href="{{ route('search', ['vegetarian' => 1]) }}" class="filter-btn bg-green-100 text-green-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-green-200 transition-colors">
                    <i class="fas fa-leaf mr-1"></i> Vegetarian
                </a>
                <a href="{{ route('search', ['popular' => 1]) }}" class="filter-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-yellow-200 transition-colors">
                    <i class="fas fa-star mr-1"></i> Popular
                </a>
                <a href="{{ route('search', ['order_type' => 'bulk']) }}" class="filter-btn bg-blue-100 text-blue-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-blue-200 transition-colors">
                    <i class="fas fa-weight mr-1"></i> Bulk Order
                </a>
                <a href="{{ route('catering.index') }}" class="filter-btn bg-purple-100 text-purple-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-purple-200 transition-colors">
                    <i class="fas fa-concierge-bell mr-1"></i> Catering
                </a>
                <a href="{{ route('menu.index') }}" class="filter-btn bg-gray-100 text-gray-800 px-4 py-2 rounded-full whitespace-nowrap hover:bg-gray-200 transition-colors">
                    <i class="fas fa-utensils mr-1"></i> All Items
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-mobile">
    <!-- Page Header -->
    <div class="py-6">
        <h1 class="text-2xl font-bold mb-4">Our Menu</h1>

        <!-- First Row Filters -->
        <div class="mb-4">
            <div class="flex flex-wrap gap-2">
                <!-- Vegetarian Filter -->
                <a href="{{ route('menu.index', array_merge(request()->query(), ['vegetarian' => request('vegetarian') ? null : 1])) }}"
                   class="filter-btn px-4 py-2 rounded-full text-sm font-medium border {{ request('vegetarian') ? 'active' : 'bg-white text-gray-700 border-gray-300' }}">
                    <i class="fas fa-leaf mr-1"></i>
                    Vegetarian
                </a>

                <!-- Popular Filter -->
                <a href="{{ route('menu.index', array_merge(request()->query(), ['popular' => request('popular') ? null : 1])) }}"
                   class="filter-btn px-4 py-2 rounded-full text-sm font-medium border {{ request('popular') ? 'active' : 'bg-white text-gray-700 border-gray-300' }}">
                    <i class="fas fa-star mr-1"></i>
                    Popular
                </a>

                <!-- Order Type Filter -->
                <select onchange="window.location.href = updateUrlParameter('order_type', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="all" {{ request('order_type') == 'all' ? 'selected' : '' }}>All Items</option>
                    <option value="unit" {{ request('order_type') == 'unit' ? 'selected' : '' }}>Unit Order</option>
                    <option value="bulk" {{ request('order_type') == 'bulk' ? 'selected' : '' }}>Bulk Order</option>
                </select>
            </div>
        </div>

        <!-- Second Row Filters -->
        <div class="mb-6">
            <div class="flex flex-wrap gap-2 mb-3">
                <!-- Category Filter -->
                <select onchange="window.location.href = updateUrlParameter('category', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>

                <!-- Cuisine Filter -->
                <select onchange="window.location.href = updateUrlParameter('cuisine', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="">All Cuisines</option>
                    @foreach($cuisines as $cuisine)
                        <option value="{{ $cuisine->id }}" {{ request('cuisine') == $cuisine->id ? 'selected' : '' }}>
                            {{ $cuisine->name }}
                        </option>
                    @endforeach
                </select>

                <!-- Sort Filter -->
                <select onchange="window.location.href = updateUrlParameter('sort', this.value)"
                        class="filter-btn px-4 py-2 rounded-full text-sm font-medium border bg-white text-gray-700 border-gray-300">
                    <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Sort by Name</option>
                    <option value="price" {{ request('sort') == 'price' ? 'selected' : '' }}>Sort by Price</option>
                    <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Sort by Popularity</option>
                </select>
            </div>

            <!-- Clear Filters -->
            @if(request()->hasAny(['category', 'cuisine', 'vegetarian', 'popular', 'order_type', 'sort']))
                <a href="{{ route('menu.index') }}" class="text-orange-600 text-sm font-medium">
                    <i class="fas fa-times mr-1"></i>Clear All Filters
                </a>
            @endif
        </div>
    </div>

    <!-- Menu Tabs -->
    <div class="mb-6">
        <div class="flex border-b border-gray-200">
            <button onclick="showTab('packages')" id="packages-tab" class="tab-btn px-4 py-2 font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300">
                Packages
            </button>
            <button onclick="showTab('food-items')" id="food-items-tab" class="tab-btn px-4 py-2 font-medium text-orange-600 border-b-2 border-orange-600">
                Food Items
            </button>
        </div>
    </div>

    <!-- Packages Section -->
    <div id="packages-section" class="hidden">
        @if($packages->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                @foreach($packages as $package)
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        @if($package->image)
                            <img src="{{ $package->image }}" alt="{{ $package->name }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-3xl"></i>
                            </div>
                        @endif
                        @if($package->is_popular)
                            <span class="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                <i class="fas fa-star mr-1"></i>Popular
                            </span>
                        @endif
                        @if($package->discount_percentage > 0)
                            <span class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                {{ $package->discount_percentage }}% OFF
                            </span>
                        @endif
                    </div>
                    <div class="p-4">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-semibold text-lg">{{ $package->name }}</h3>
                            <div class="flex space-x-1">
                                @if($package->is_vegetarian)
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                                        <i class="fas fa-leaf"></i>
                                    </span>
                                @endif
                            </div>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">{{ $package->description }}</p>

                        <!-- Package Items Preview -->
                        @if($package->foodItems->count() > 0)
                            <div class="mb-3">
                                <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                <div class="text-xs text-gray-600">
                                    @foreach($package->foodItems->take(3) as $item)
                                        <span>{{ $item->name }}{{ !$loop->last ? ', ' : '' }}</span>
                                    @endforeach
                                    @if($package->foodItems->count() > 3)
                                        <span class="text-orange-600">+{{ $package->foodItems->count() - 3 }} more</span>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xl font-bold text-orange-600">${{ number_format($package->price, 2) }}</span>
                                    @if($package->original_price && $package->original_price > $package->price)
                                        <span class="text-sm text-gray-500 line-through">${{ number_format($package->original_price, 2) }}</span>
                                    @endif
                                </div>
                                @if($package->serves_people)
                                    <p class="text-xs text-gray-500">Serves {{ $package->serves_people }} people</p>
                                @endif
                            </div>
                            <div class="flex space-x-2">
                                <a href="{{ route('menu.package', $package->slug) }}" class="bg-gray-100 text-gray-700 px-3 py-2 rounded text-sm hover:bg-gray-200 transition-colors">
                                    View Details
                                </a>
                                <button onclick="addToCart('package', {{ $package->id }})" class="bg-orange-600 text-white px-4 py-2 rounded text-sm hover:bg-orange-700 transition-colors">
                                    Add to Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Packages Pagination -->
            <div class="mb-8">
                {{ $packages->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-box-open text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No packages found matching your criteria.</p>
            </div>
        @endif
    </div>

    <!-- Food Items Section -->
    <div id="food-items-section">
        @if($foodItems->count() > 0)
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
                @foreach($foodItems as $item)
                <div class="food-card bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="relative">
                        @if($item->image)
                            <img src="{{ $item->image }}" alt="{{ $item->name }}" class="w-full h-32 object-cover">
                        @else
                            <div class="w-full h-32 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                        @endif
                        <div class="absolute top-1 left-1 flex space-x-1">
                            @if($item->is_vegetarian)
                                <span class="bg-green-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-leaf"></i>
                                </span>
                            @endif
                            @if($item->is_popular)
                                <span class="bg-yellow-500 text-white px-1 py-0.5 rounded text-xs">
                                    <i class="fas fa-star"></i>
                                </span>
                            @endif
                        </div>
                        @if($item->is_spicy)
                            <span class="absolute top-1 right-1 bg-red-500 text-white px-1 py-0.5 rounded text-xs">
                                <i class="fas fa-pepper-hot"></i>
                            </span>
                        @endif
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-sm mb-1">{{ $item->name }}</h3>
                        <p class="text-xs text-gray-600 mb-2">{{ $item->category->name }} • {{ $item->cuisine->name }}</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-bold text-orange-600">${{ number_format($item->price_per_unit, 2) }}</span>
                                @if($item->allow_bulk_order && $item->price_per_kg)
                                    <span class="text-xs text-gray-500 block">${{ number_format($item->price_per_kg, 2) }}/kg</span>
                                @endif
                            </div>
                            <div class="flex space-x-1">
                                <a href="{{ route('menu.food-item', $item->slug) }}" class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                                    View
                                </a>
                                <button onclick="addToCart('food_item', {{ $item->id }})" class="bg-orange-600 text-white px-2 py-1 rounded text-xs hover:bg-orange-700 transition-colors">
                                    Add
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Food Items Pagination -->
            <div class="mb-8">
                {{ $foodItems->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-utensils text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-600">No food items found matching your criteria.</p>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
    // Tab functionality
    function showTab(tabName) {
        // Hide all sections
        document.getElementById('packages-section').classList.add('hidden');
        document.getElementById('food-items-section').classList.add('hidden');

        // Remove active class from all tabs
        document.getElementById('packages-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('packages-tab').classList.add('text-gray-500', 'border-transparent');
        document.getElementById('food-items-tab').classList.remove('text-orange-600', 'border-orange-600');
        document.getElementById('food-items-tab').classList.add('text-gray-500', 'border-transparent');

        // Show selected section and activate tab
        if (tabName === 'packages') {
            document.getElementById('packages-section').classList.remove('hidden');
            document.getElementById('packages-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('packages-tab').classList.remove('text-gray-500', 'border-transparent');
        } else {
            document.getElementById('food-items-section').classList.remove('hidden');
            document.getElementById('food-items-tab').classList.add('text-orange-600', 'border-orange-600');
            document.getElementById('food-items-tab').classList.remove('text-gray-500', 'border-transparent');
        }
    }

    // URL parameter update function
    function updateUrlParameter(param, value) {
        const url = new URL(window.location);
        if (value && value !== 'all' && value !== '') {
            url.searchParams.set(param, value);
        } else {
            url.searchParams.delete(param);
        }
        return url.toString();
    }

    // Initialize default tab
    document.addEventListener('DOMContentLoaded', function() {
        showTab('food-items');
    });
</script>
@endpush
@endsection
